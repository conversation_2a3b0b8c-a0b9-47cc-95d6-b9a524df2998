import * as React from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './MarkdownRenderer'
import 'katex/dist/katex.min.css'
import CircularProgress from '@mui/material/CircularProgress';

import { Handle, Position, useNodeId, NodeToolbar, NodeResizer, useStore, useReactFlow, useViewport, useNodes } from '@xyflow/react';

import { AIActionList } from './AIActionList';
import { TodoList } from './TodoList';
import { ColorMenu, node_color_themes } from './ColorMenu';
import { useToast, useBreakpointValue, Tooltip, useMediaQuery } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { Link } from '@styled-icons/entypo/Link';
import { FileText } from '@styled-icons/bootstrap/FileText'
import { AIFLOW_TOAST } from '@/common/constants/actionTypes';
import { useDispatch } from 'react-redux';
import { useIntl } from 'react-intl';

//- text (for detailed explanations, narrative content, or topics requiring coherent exposition)
//- mindmap (for hierarchical information, multi-connected concepts, or topics needing an overall framework)
//- todo_list (for tasks with clear steps or action items, especially those to be completed sequentially)


export const selector = (state) => ({
  nodes: state.nodes,
  setNodes: state.setNodes,
  addSubNode: state.addSubNode,
  addNode: state.addNode,
  deleteNode: state.deleteNode,
  getNode: state.getNode,
  updateNode: state.updateNode,
  updateNodeData: state.updateNodeData,
  edges: state.edges,
  getNodeEdges: state.getNodeEdges
});

const NodeTitle = ({ title, url, loading, info, color_theme, nodeType, queryType }) => {
  const [isOverflown, setIsOverflown] = React.useState(false);
  const containerRef = React.useRef(null);

  React.useEffect(() => {
    if (containerRef.current) {
      setIsOverflown(containerRef.current.scrollHeight > containerRef.current.clientHeight);
    }
  }, [title]);

  const styles = React.useMemo(() => ({
    container: {
      display: '-webkit-box',
      WebkitLineClamp: 2,
      whiteSpace: 'pre-line',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      WebkitBoxOrient: 'vertical',
      // color: !!url ? 'dodgerblue' : undefined
    }
  }), [color_theme]);

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: color_theme.title_bg,
      borderBottomWidth: '1px',
      borderColor: color_theme.border,
      padding: '12px',
      paddingTop: 5,
      paddingBottom: 3,
      paddingLeft: (queryType === 'link' || nodeType === 'funblocks_doc') && 6 || 12,
      minHeight: 20,
      fontSize: 14,
      fontWeight: 500,
      borderTopLeftRadius: 3,
      borderTopRightRadius: 3,
    }}>
      {
        (queryType === 'link' || nodeType === 'funblocks_doc') &&
        <div style={{ width: 18, height: 24, marginRight: 4, display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
          {
            queryType === 'link' && <Link size={18} color={color_theme.border} />
          }
          {
            nodeType === 'funblocks_doc' && <FileText size={18} color={color_theme.border} />
          }
        </div>
      }


      <div ref={containerRef} style={styles.container}>
        {isOverflown ? (
          <Tooltip label={title} aria-label="expand title tooltip">
            <div style={{ height: 'fit-content' }}>{title}</div>
          </Tooltip>
        ) : (
          <div>{title}</div>
        )}
      </div>
      {
        loading && <CircularProgress size={18} />
      }
      {
        !loading && !!info &&
        <span style={{
          fontSize: 13
        }}>
          ({info})
        </span>
      }
    </div>
  );
};


const AINode = ({ data, isConnectable, selected }) => {
  const dispatch = useDispatch();

  const [ai_menu_anchor, set_ai_menu_anchor] = React.useState();
  const [sub_menu_anchor, set_sub_menu_anchor] = React.useState();
  const intl = useIntl();

  const selectedItemRef = React.useRef(null);
  const contentContainerRef = React.useRef(null);
  const videoContainerRef = React.useRef(null);

  const [itemTargeted, setItemTargeted] = React.useState(0);
  const [sub_menu_parent, set_sub_menu_parent] = React.useState(-1);
  const [sub_menu_items, set_sub_menu_items] = React.useState([]);
  const [sub_menu_item_targeted, set_sub_menu_item_targeted] = React.useState(0);
  const [sub_menu_visible, set_sub_menu_visible] = React.useState(false);
  // const [avaliableAssistantItems, setAvaliableAssistantItems] = React.useState(assistant_items);
  // const [extendedAssistantItems, setExtendedAssistantItems] = React.useState([]);
  // const [filteredAssistantItems, setFilteredAssistantItems] = React.useState([]);
  // const [groupedAssistantItems, setGroupedAssistantItems] = React.useState([]);
  // const [drafter, setDrafter] = React.useState([]);
  // const [selectedText, setSelectedText] = React.useState();
  // const [selectedTopic, setSelectedTopic] = React.useState();



  const [aigc_hovered, set_aigc_hovered] = React.useState();
  const [sub_menu_hovered, set_sub_menu_hovered] = React.useState();
  const [menuDroppedDown, setMenuDroppedDown] = React.useState();
  const [form, setForm] = React.useState();
  const { zoom } = useViewport();

  const [isMobile] = useMediaQuery("(max-width: 768px)")

  const nodeId = useNodeId();
  const nodes = useNodes();
  const node = nodes.find(node => node.id === nodeId)
  const { nodeType, title, content } = data;
  const defaultWidth = React.useMemo(() => nodeType === 'slides' || data.ai_action === 'slideshow' ? 480 : 340, []);
  const color_theme = React.useMemo(() => node_color_themes.find(theme => theme.id === data.color_theme) || node_color_themes.find(theme => theme.id === 'blue'), [data.color_theme]);

  const showToast = React.useCallback((toast) => {
    dispatch({
        type: AIFLOW_TOAST,
        value: toast
    })
}, [])

  const aiItemClicked = React.useCallback(() => {
    showToast({
      msg: intl.formatMessage({ id: 'to_aiflow' }),
      action: 'to_aiflow',
      done: false
    })
  }, [showToast, intl]);

  return (
    <div
      // ref={nodeRef}
      className='node'
      style={nodeType == 'group' ? {
        display: 'contents'
      } : {
        backgroundColor: color_theme.content_bg,
        borderColor: color_theme.border,
        boxShadow: `0px 0px 6px #ccc`,
        borderRadius: nodeType == 'note' ? 0 : undefined,
        width: nodeType === 'video' && 562 || node?.width || defaultWidth,
        height: nodeType === 'video' && 'fit-content' || undefined,
        minHeight: data.ai_action === 'slideshow' ? defaultWidth * 9 / 16 : undefined,
        maxHeight: data.minimized ? 100 : undefined,
        overflowY: data.minimized ? 'clip' : undefined,
        pointerEvents: 'all'
      }}
    // onMouseEnter={() => set_aigc_hovered(true)}
    // onMouseLeave={() => set_aigc_hovered(false)}
    >

      <Handle type="target" position={Position.Top} id="TT" />
      <Handle type="target" position={Position.Bottom} id="BT" />
      <Handle type="target" position={Position.Left} id='LT' />
      <Handle type="target" position={Position.Right} id="RT" />
      <Handle type="source" position={Position.Top} id="a" />
      <Handle type="source" position={Position.Right} id="b" />
      <Handle type="source" position={Position.Bottom} id="c" />
      <Handle type="source" position={Position.Left} id="d" />


      <>
        {

          <>
            {
              // !['brainstorming_perspective'].includes(data.ai_action) &&
              !['image'].includes(nodeType) &&
              <NodeTitle
                nodeType={nodeType}
                queryType={data.queryType}
                // loading={nodeType === 'video' && context_loading}
                title={title}
                url={data.context?.url}
                color_theme={color_theme}
              />
            }
            {
              !!data.vid &&
              <iframe width="560" height="315"
                ref={videoContainerRef}
                src={`https://www.youtube.com/embed/${data.vid}`}
                title="FunBlocks AI Video Player" frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                referrerPolicy="strict-origin-when-cross-origin"
                allowFullScreen>

              </iframe>
            }
            {

              !!data.content?.src &&
              <img
                src={data.content.src}
                style={{ borderRadius: 4 }}
              />
            }
            {
              (data.queryType === 'link' || ['funblocks_doc'].includes(data.nodeType) || content && nodeType !== 'image' || data.brainstorming_scenario || data.todos || data.items) &&
              <div
                className={isMobile || !selected ? undefined : 'nodrag'}
                style={{
                  margin: '12px',
                  marginRight: (data.todos || data.items) ? 5 : 12,
                  marginTop: 7,
                  marginBottom: 7,
                  fontSize: 14,
                  overflowY: 'auto',
                  cursor: 'text',
                  // display: 'contents'
                  // minHeight: 40
                }}
                ref={contentContainerRef}
              >
                {
                  content && !['breakdown', 'brainstorming_perspective', 'slideshow'].includes(data.ai_action) && !data.todos?.length &&
                  <MarkdownRenderer content={content} />
                }

                {
                  !!data.brainstorming_scenario &&
                  <div style={{
                    fontSize: 13,
                    color: 'GrayText'
                  }}>
                    {
                      data.brainstorming_scenario
                    }
                  </div>
                }

                {
                  data.items &&
                  // ['breakdown'].includes(data.ai_action) &&
                  <AIActionList
                    nodeId={nodeId}
                    list={data.items}
                    queryType={'tell_more'}
                    // queryType={data.ai_action == 'breakdown' && 'tell_more' || data.queryType === 'brainstorming_insights' && 'tell_more' || undefined}
                    // action={data.queryType === 'perspective' && 'breakdown' || undefined}
                    aiItemClicked={aiItemClicked}

                    expandEnabled={true}
                    onExpand={(anchor, topic) => {
                      aiItemClicked();
                    }}
                  />
                }

                {
                  data.todos &&
                  <TodoList
                    nodeId={nodeId}
                    list={data.todos}
                    priorities={data.priorities}
                    queryType={'task_breakdown'}
                    aiItemClicked={aiItemClicked}
                    expandEnabled={true}
                    onExpand={(anchor, topic) => {
                      aiItemClicked();
                    }}

                    updateNodeData={(data) => {
                      aiItemClicked();
                    }}
                    handleUpdatePriority={aiItemClicked}
                  />
                }

                {
                  (['link'].includes(data.queryType) || ['funblocks_doc'].includes(data.nodeType)) && !['prompt', 'note'].includes(data.nodeType) &&
                  <div
                    className='fill-available'
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'flex-end'
                    }}
                  >
                    <div
                      onClick={async () => {
                        let url = data.context?.url;
                        if (nodeType === 'funblocks_doc') {
                          url = `${window.location.origin}/#/editor?hid=${data.hid}&space=workspace`
                        }
                        window.open(url, '_blank');
                      }}
                      style={{
                        color: color_theme.border,
                        border: 'none',
                        padding: '8px 12px',
                        cursor: 'pointer',
                        textAlign: 'center'
                      }}
                    >
                      {intl.formatMessage({ id: 'to_original_page' })}
                    </div>
                  </div>
                }
              </div>
            }

          </>
        }
      </>
    </div >
  )
}

const styles = {
  container: {
    backgroundColor: 'white',
    border: '1px solid gray',
    boxShadow: '0px 0px 8px #bbb',
    margin: '6px',
    marginLeft: 0,
    borderRadius: '4px'
  },
  contentSect: {
    fontSize: 13,
    color: 'gray'
  },
  toolbarIcon: {
    width: 35,
    height: 35,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  }
}

export default AINode;
